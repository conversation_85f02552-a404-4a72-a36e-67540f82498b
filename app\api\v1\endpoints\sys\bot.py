from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.sys.bot import SysBotCreate, SysBotUpdate, SysBotResponse, SysBotListResponse
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.sys.bot import create_bot, get_bot, get_bots, update_bot, delete_bot, get_active_bots
from app.core.deps import get_super_admin_user

router = APIRouter()


@router.post("/", response_model=SysBotResponse, summary="创建机器人")
def create_sys_bot(
    bot_data: SysBotCreate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建机器人（仅超级管理员可操作）
    
    - **name**: 机器人名称
    - **api_endpoint**: API路径
    - **api_key**: API密钥
    - **sys_prompt**: 系统提示词模板
    - **notes**: 备注
    - **active**: 是否有效
    """
    return create_bot(db, bot_data)


@router.get("/{bot_id}", response_model=SysBotResponse, summary="获取机器人详情")
def get_sys_bot(
    bot_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取机器人详情（仅超级管理员可操作）
    """
    bot = get_bot(db, bot_id)
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="机器人不存在"
        )
    return bot


@router.get("/", response_model=dict, summary="获取机器人列表")
def get_sys_bots(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    active: Optional[int] = Query(None, description="状态过滤"),
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取机器人列表（仅超级管理员可操作）
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **search**: 搜索关键词（机器人名称）
    - **active**: 状态过滤（0：失效；1：有效）
    """
    pagination = PaginationParams(page=page, page_size=page_size)
    bots, total = get_bots(db, pagination, search, active)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": bots,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.get("/active/list", response_model=list[SysBotListResponse], summary="获取有效机器人列表")
def get_active_sys_bots(
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取所有有效的机器人列表（仅超级管理员可操作）
    """
    return get_active_bots(db)


@router.put("/{bot_id}", response_model=SysBotResponse, summary="更新机器人")
def update_sys_bot(
    bot_id: int,
    bot_data: SysBotUpdate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新机器人（仅超级管理员可操作）
    
    - **name**: 机器人名称
    - **api_endpoint**: API路径
    - **api_key**: API密钥
    - **sys_prompt**: 系统提示词模板
    - **notes**: 备注
    - **active**: 是否有效
    """
    bot = update_bot(db, bot_id, bot_data)
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="机器人不存在"
        )
    return bot


@router.delete("/{bot_id}", response_model=ResponseModel, summary="删除机器人")
def delete_sys_bot(
    bot_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除机器人（软删除，仅超级管理员可操作）
    """
    success = delete_bot(db, bot_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="机器人不存在"
        )
    
    return ResponseModel(message="机器人删除成功")
