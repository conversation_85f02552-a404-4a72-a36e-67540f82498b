from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.sys.admin import SysAdminCreate, SysAdminUpdate, SysAdminResponse, SysAdminListResponse
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.sys.admin import create_admin, get_admin, get_admins, update_admin, delete_admin
from app.core.deps import get_super_admin_user

router = APIRouter()


@router.post("/", response_model=SysAdminResponse, summary="创建系统管理员")
def create_sys_admin(
    admin_data: SysAdminCreate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建系统管理员（仅超级管理员可操作）
    
    - **uid**: 用户ID
    - **name**: 姓名
    - **role**: 角色（0：超级管理员；1：管理员）
    """
    return create_admin(db, admin_data)


@router.get("/{admin_id}", response_model=SysAdminResponse, summary="获取系统管理员详情")
def get_sys_admin(
    admin_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统管理员详情（仅超级管理员可操作）
    """
    admin = get_admin(db, admin_id)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    return admin


@router.get("/", response_model=dict, summary="获取系统管理员列表")
def get_sys_admins(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统管理员列表（仅超级管理员可操作）
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **search**: 搜索关键词（姓名或用户名）
    """
    pagination = PaginationParams(page=page, page_size=page_size)
    admins, total = get_admins(db, pagination, search)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": admins,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.put("/{admin_id}", response_model=SysAdminResponse, summary="更新系统管理员")
def update_sys_admin(
    admin_id: int,
    admin_data: SysAdminUpdate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新系统管理员（仅超级管理员可操作）
    
    - **name**: 姓名
    - **role**: 角色
    """
    admin = update_admin(db, admin_id, admin_data)
    if not admin:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    return admin


@router.delete("/{admin_id}", response_model=ResponseModel, summary="删除系统管理员")
def delete_sys_admin(
    admin_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除系统管理员（仅超级管理员可操作）
    """
    success = delete_admin(db, admin_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="管理员不存在"
        )
    
    return ResponseModel(message="管理员删除成功")
