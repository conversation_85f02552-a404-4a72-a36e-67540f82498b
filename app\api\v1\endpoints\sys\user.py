from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.sys.user import SysUserCreate, SysUserUpdate, SysUserResponse, SysUserListResponse
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.sys.user import create_user, get_user, get_users, update_user, delete_user
from app.core.deps import get_super_admin_user

router = APIRouter()


@router.post("/", response_model=SysUserResponse, summary="创建系统用户")
def create_sys_user(
    user_data: SysUserCreate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建系统用户（仅超级管理员可操作）
    
    - **username**: 用户名
    - **password**: 密码
    - **active**: 是否有效（0：失效；1：有效）
    """
    return create_user(db, user_data)


@router.get("/{user_id}", response_model=SysUserResponse, summary="获取系统用户详情")
def get_sys_user(
    user_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统用户详情（仅超级管理员可操作）
    """
    user = get_user(db, user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.get("/", response_model=dict, summary="获取系统用户列表")
def get_sys_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统用户列表（仅超级管理员可操作）
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **search**: 搜索关键词（用户名）
    """
    pagination = PaginationParams(page=page, page_size=page_size)
    users, total = get_users(db, pagination, search)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": users,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.put("/{user_id}", response_model=SysUserResponse, summary="更新系统用户")
def update_sys_user(
    user_id: int,
    user_data: SysUserUpdate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新系统用户（仅超级管理员可操作）
    
    - **username**: 用户名
    - **password**: 密码
    - **active**: 是否有效
    """
    user = update_user(db, user_id, user_data)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    return user


@router.delete("/{user_id}", response_model=ResponseModel, summary="删除系统用户")
def delete_sys_user(
    user_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除系统用户（软删除，仅超级管理员可操作）
    """
    success = delete_user(db, user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return ResponseModel(message="用户删除成功")
