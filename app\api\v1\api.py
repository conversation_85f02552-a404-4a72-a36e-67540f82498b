from fastapi import APIRouter

from app.api.v1.endpoints import system
from app.api.v1.endpoints.sys import auth as sys_auth, user as sys_user, admin as sys_admin, bot as sys_bot, bconf as sys_bconf, tenant as sys_tenant
from app.api.v1.endpoints.tnt import auth as tnt_auth, admin as tnt_admin

api_router = APIRouter()

# System endpoints
api_router.include_router(system.router, prefix="/sys", tags=["system"])

# System management endpoints
api_router.include_router(sys_auth.router, prefix="/sys/auth", tags=["sys-auth"])
api_router.include_router(sys_user.router, prefix="/sys/user", tags=["sys-user"])
api_router.include_router(sys_admin.router, prefix="/sys/admin", tags=["sys-admin"])
api_router.include_router(sys_bot.router, prefix="/sys/bot", tags=["sys-bot"])
api_router.include_router(sys_bconf.router, prefix="/sys/bconf", tags=["sys-bconf"])
api_router.include_router(sys_tenant.router, prefix="/sys/tenant", tags=["sys-tenant"])

# Tenant management endpoints
api_router.include_router(tnt_auth.router, prefix="/tnt/auth", tags=["tnt-auth"])
api_router.include_router(tnt_admin.router, prefix="/tnt/admin", tags=["tnt-admin"])
