from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas.sys.bconf import SysBconfCreate, SysBconfUpdate, SysBconfResponse, SysBconfListResponse
from app.schemas.base import PaginationParams, ResponseModel, PaginationResponse
from app.services.sys.bconf import create_bconf, get_bconf, get_bconfs, update_bconf, delete_bconf, get_bconfs_by_bot
from app.core.deps import get_super_admin_user

router = APIRouter()


@router.post("/", response_model=SysBconfResponse, summary="创建系统机器人配置")
def create_sys_bconf(
    bconf_data: SysBconfCreate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    创建系统机器人配置（仅超级管理员可操作）
    
    - **key**: 配置key
    - **bid**: 机器人ID
    - **notes**: 备注
    """
    return create_bconf(db, bconf_data)


@router.get("/{key}", response_model=SysBconfResponse, summary="获取系统机器人配置详情")
def get_sys_bconf(
    key: str,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统机器人配置详情（仅超级管理员可操作）
    """
    bconf = get_bconf(db, key)
    if not bconf:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    return bconf


@router.get("/", response_model=dict, summary="获取系统机器人配置列表")
def get_sys_bconfs(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    bot_id: Optional[int] = Query(None, description="机器人ID过滤"),
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取系统机器人配置列表（仅超级管理员可操作）
    
    - **page**: 页码
    - **page_size**: 每页数量
    - **bot_id**: 机器人ID过滤
    """
    pagination = PaginationParams(page=page, page_size=page_size)
    bconfs, total = get_bconfs(db, pagination, bot_id)
    
    pages = (total + page_size - 1) // page_size
    
    return {
        "items": bconfs,
        "pagination": PaginationResponse(
            total=total,
            page=page,
            page_size=page_size,
            pages=pages
        )
    }


@router.get("/bot/{bot_id}/list", response_model=list[SysBconfListResponse], summary="获取指定机器人的配置列表")
def get_sys_bconfs_by_bot(
    bot_id: int,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    获取指定机器人的所有配置（仅超级管理员可操作）
    """
    return get_bconfs_by_bot(db, bot_id)


@router.put("/{key}", response_model=SysBconfResponse, summary="更新系统机器人配置")
def update_sys_bconf(
    key: str,
    bconf_data: SysBconfUpdate,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    更新系统机器人配置（仅超级管理员可操作）
    
    - **bid**: 机器人ID
    - **notes**: 备注
    """
    bconf = update_bconf(db, key, bconf_data)
    if not bconf:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    return bconf


@router.delete("/{key}", response_model=ResponseModel, summary="删除系统机器人配置")
def delete_sys_bconf(
    key: str,
    current_user: dict = Depends(get_super_admin_user),
    db: Session = Depends(get_db)
):
    """
    删除系统机器人配置（仅超级管理员可操作）
    """
    success = delete_bconf(db, key)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置不存在"
        )
    
    return ResponseModel(message="配置删除成功")
